# Airtable Integration Guide

## Overview

The Attribution Dashboard now includes comprehensive Airtable integration that allows you to:

- 🔍 **Discover existing Airtable bases** and their table structures
- 🏗️ **Create new client bases** with pre-configured table templates
- ⚙️ **Auto-generate client configurations** with correct table IDs
- 📋 **Manage multiple data sources** (GHL, Google Ads, POS, Meta Ads)

## Getting Started

### 1. Prerequisites

- Airtable API token with appropriate permissions
- Python environment with required dependencies

### 2. Setting Up API Access

1. **Get your Airtable API token:**
   - Go to https://airtable.com/account
   - Navigate to "API" section
   - Generate a personal access token with the following scopes:
     - `data.records:read`
     - `data.records:write`
     - `schema.bases:read`
     - `schema.bases:write`

2. **Set environment variable:**
   ```bash
   export AIRTABLE_API_KEY="your_token_here"
   ```

### 3. Using the Airtable Tab

1. **Launch the customizer:**
   ```bash
   python attrcust.py
   ```

2. **Navigate to the "🗃️ Airtable Setup" tab**

3. **Configure API access:**
   - Enter your API token
   - Click "🔗 Test Connection"
   - Verify successful connection

## Features

### Base Discovery

- **Refresh Bases**: Lists all accessible Airtable bases
- **View Base Schema**: Shows detailed table and field information
- **Base Selection**: Click on any base to view its structure

### Client Base Creation

1. **Enter client name** (e.g., "ABC Auto Repair")
2. **Select workspace** (optional)
3. **Click "🏗️ Create Client Base"**

This creates a new Airtable base with four pre-configured tables:

#### GHL Leads Table
- Contact Name, Email, Phone
- Date Created, Lead Source, Status
- Tags, Notes

#### Google Ads Data Table
- Campaign Name, Date
- Impressions, Clicks, Cost
- CTR, CPC, Conversions
- Ad Group, Keywords

#### POS Sales Data Table
- Transaction ID, Customer info
- Created date, Sale Amount
- Payment Method, Service Type
- Location, Notes

#### Meta Ads Data Table
- Campaign/Ad Set/Ad Name
- Reporting date, Impressions, Clicks
- Spend, CTR, CPC, CPM
- Platform, Objective

### Configuration Generation

After creating or selecting a base:

1. **Auto-detection**: System automatically identifies table types
2. **Configuration generation**: Creates complete client_config.json
3. **Preview**: Review generated configuration
4. **Actions available:**
   - 📋 Copy to clipboard
   - 💾 Save to client_config.json
   - 🔄 Auto-configure customizer

### Using Existing Bases

1. **Select existing base** from the list
2. **Click "📊 View Base Schema"** to inspect structure
3. **Click "📋 Use Selected Base"** to generate configuration
4. **System maps tables** based on naming conventions:
   - Tables with "ghl" or "lead" → GHL data source
   - Tables with "google" and "ads" → Google Ads data source
   - Tables with "pos" or "sales" → POS data source
   - Tables with "meta" or "facebook" → Meta Ads data source

## Configuration Schema

Generated configurations follow this structure:

```json
{
  "client_info": {
    "client_id": "client_name",
    "business_name": "Client Business Name",
    "created_date": "2025-01-08T00:00:00Z",
    "last_updated": "2025-01-08T00:00:00Z"
  },
  "data_sources": {
    "enabled_sources": ["ghl", "google_ads"],
    "disabled_sources": ["pos", "meta_ads"],
    "source_configs": {
      "ghl": {
        "table_id": "tblXXXXXXXXXXXXXX",
        "cache_ttl": 300,
        "date_field": "Date Created"
      },
      "google_ads": {
        "table_id": "tblYYYYYYYYYYYYYY",
        "cache_ttl": 300,
        "date_field": "Date"
      }
    }
  },
  "tab_configuration": {
    "enabled_tabs": ["overview", "ghl", "google_ads"],
    "tab_order": ["overview", "ghl", "google_ads"],
    "default_tab": "overview",
    "tab_labels": {
      "overview": "Master Overview",
      "ghl": "GHL Analytics",
      "google_ads": "Google Ads"
    }
  },
  "airtable_configuration": {
    "base_id": "appXXXXXXXXXXXXXX",
    "api_key_env_var": "AIRTABLE_API_KEY"
  }
}
```

## Testing

Run the test suite to validate your setup:

```bash
python test_airtable_integration.py
```

This tests:
- ✅ API connection
- ✅ Base discovery
- ✅ Schema retrieval
- ✅ Workspace access
- ✅ Table templates
- ✅ Configuration generation

## Troubleshooting

### Common Issues

1. **"Authentication failed"**
   - Verify API token is correct
   - Check token permissions include required scopes

2. **"No bases found"**
   - Ensure token has access to at least one base
   - Check workspace permissions

3. **"Table creation failed"**
   - Verify token has write permissions
   - Check workspace limits

4. **"Configuration not applied"**
   - Ensure client name is entered
   - Check that base was selected or created successfully

### API Limits

- Airtable API has rate limits (5 requests per second)
- Large bases may take time to process
- Consider pagination for bases with many tables

## Best Practices

1. **Use descriptive client names** for easy identification
2. **Test with existing bases first** before creating new ones
3. **Backup configurations** before making changes
4. **Use workspaces** to organize client bases
5. **Follow naming conventions** for automatic table detection

## Next Steps

After setting up Airtable integration:

1. **Configure data sources** in the "🔌 Data Sources" tab
2. **Customize branding** in the "🎨 Client Customization" tab
3. **Generate client instance** in the "🚀 Generate Dashboard" tab
4. **Deploy to Railway** using the "🚀 Deployment" tab

## Support

For issues or questions:
- Check the logs in the "📋 Logs & Debug" tab
- Run the test suite for diagnostics
- Review Airtable API documentation for advanced usage
