{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Attribution Dashboard Tab Configuration", "description": "Schema for client-specific tab and data source configuration", "type": "object", "properties": {"client_info": {"type": "object", "properties": {"client_id": {"type": "string", "description": "Unique identifier for the client"}, "business_name": {"type": "string", "description": "Client business name"}, "created_date": {"type": "string", "format": "date-time", "description": "Configuration creation date"}, "last_updated": {"type": "string", "format": "date-time", "description": "Last configuration update date"}}, "required": ["client_id", "business_name"]}, "data_sources": {"type": "object", "properties": {"enabled_sources": {"type": "array", "items": {"type": "string", "enum": ["ghl", "google_ads", "pos", "meta_ads", "meta_ads_summary", "meta_ads_simplified"]}, "description": "List of enabled data sources for this client", "default": ["ghl", "google_ads"]}, "disabled_sources": {"type": "array", "items": {"type": "string", "enum": ["ghl", "google_ads", "pos", "meta_ads", "meta_ads_summary", "meta_ads_simplified"]}, "description": "List of explicitly disabled data sources"}, "source_configs": {"type": "object", "properties": {"ghl": {"type": "object", "properties": {"table_id": {"type": "string", "description": "Airtable table ID for GHL data"}, "cache_ttl": {"type": "integer", "default": 300, "description": "<PERSON><PERSON> in seconds"}, "date_field": {"type": "string", "default": "Date Created"}}}, "google_ads": {"type": "object", "properties": {"table_id": {"type": "string", "description": "Airtable table ID for Google Ads data"}, "cache_ttl": {"type": "integer", "default": 300, "description": "<PERSON><PERSON> in seconds"}, "date_field": {"type": "string", "default": "Date"}}}, "pos": {"type": "object", "properties": {"table_id": {"type": "string", "description": "Airtable table ID for POS data"}, "cache_ttl": {"type": "integer", "default": 300, "description": "<PERSON><PERSON> in seconds"}, "date_field": {"type": "string", "default": "Created"}}}, "meta_ads": {"type": "object", "properties": {"table_id": {"type": "string", "description": "Airtable table ID for Meta Ads data"}, "cache_ttl": {"type": "integer", "default": 300, "description": "<PERSON><PERSON> in seconds"}, "date_field": {"type": "string", "default": "Reporting ends"}}}}}}, "required": ["enabled_sources"]}, "tab_configuration": {"type": "object", "properties": {"enabled_tabs": {"type": "array", "items": {"type": "string", "enum": ["overview", "ghl", "google_ads", "pos", "meta_ads", "sales_report", "lead_report"]}, "description": "List of enabled dashboard tabs", "default": ["overview", "ghl", "google_ads"]}, "tab_order": {"type": "array", "items": {"type": "string"}, "description": "Order of tabs in the dashboard navigation"}, "default_tab": {"type": "string", "description": "Default tab to show when dashboard loads", "default": "overview"}, "tab_labels": {"type": "object", "properties": {"overview": {"type": "string", "default": "Master Overview"}, "ghl": {"type": "string", "default": "GHL Analytics"}, "google_ads": {"type": "string", "default": "Google Ads"}, "pos": {"type": "string", "default": "POS Analytics"}, "meta_ads": {"type": "string", "default": "Meta Ads"}, "sales_report": {"type": "string", "default": "Sales Report"}, "lead_report": {"type": "string", "default": "Lead Report"}}, "description": "Custom labels for dashboard tabs"}}, "required": ["enabled_tabs"]}, "ui_configuration": {"type": "object", "properties": {"hide_disabled_features": {"type": "boolean", "default": true, "description": "Whether to hide UI elements for disabled features"}, "show_data_source_indicators": {"type": "boolean", "default": false, "description": "Whether to show indicators for which data sources are active"}, "simplified_navigation": {"type": "boolean", "default": false, "description": "Use simplified navigation for clients with fewer tabs"}}}, "performance_settings": {"type": "object", "properties": {"lazy_load_tabs": {"type": "boolean", "default": true, "description": "Whether to lazy load tab content"}, "preload_data": {"type": "array", "items": {"type": "string"}, "description": "Data sources to preload on dashboard initialization"}, "cache_strategy": {"type": "string", "enum": ["aggressive", "moderate", "minimal"], "default": "moderate", "description": "Caching strategy for this client"}}}, "airtable_configuration": {"type": "object", "properties": {"base_id": {"type": "string", "description": "Client-specific Airtable base ID"}, "api_key_env_var": {"type": "string", "default": "AIRTABLE_API_KEY", "description": "Environment variable name for Airtable API key"}}, "required": ["base_id"]}}, "required": ["client_info", "data_sources", "tab_configuration", "airtable_configuration"]}