#!/usr/bin/env python3
"""
Test script to verify Google Ads template includes Campaign ID
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Airtable manager
from attrcust import AirtableManager

def test_google_ads_template():
    """Test that Google Ads template includes Campaign ID field"""
    
    print("🔍 GOOGLE ADS TEMPLATE TEST")
    print("=" * 40)
    
    # Initialize Airtable manager
    manager = AirtableManager()
    
    # Get Google Ads template
    template = manager._get_google_ads_table_template()
    
    print(f"📋 Template Name: {template['name']}")
    print(f"📝 Description: {template['description']}")
    print(f"📊 Total Fields: {len(template['fields'])}")
    
    print(f"\n🔍 FIELD LIST:")
    print("-" * 30)
    
    field_names = []
    for i, field in enumerate(template['fields'], 1):
        field_name = field['name']
        field_type = field['type']
        field_names.append(field_name)
        
        # Highlight Campaign ID field
        if field_name == 'Campaign ID':
            print(f"  {i:2d}. ✅ {field_name} ({field_type}) ← CAMPAIGN ID FOUND!")
        else:
            print(f"  {i:2d}. {field_name} ({field_type})")
    
    print(f"\n🎯 CAMPAIGN ID CHECK:")
    print("-" * 25)
    
    if 'Campaign ID' in field_names:
        print("✅ SUCCESS: Campaign ID field is present in template!")
        print(f"📍 Position: {field_names.index('Campaign ID') + 1} of {len(field_names)}")
    else:
        print("❌ ERROR: Campaign ID field is missing from template!")
        print("🔧 This needs to be fixed in the template.")
    
    print(f"\n📋 REQUIRED FIELDS CHECK:")
    print("-" * 30)
    
    required_fields = [
        'Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks',
        'Conversions', 'CTR', 'CPC', 'Conv. Rate', 'Cost per Conv.'
    ]
    
    missing_fields = []
    for required_field in required_fields:
        if required_field in field_names:
            print(f"  ✅ {required_field}")
        else:
            print(f"  ❌ {required_field}")
            missing_fields.append(required_field)
    
    print(f"\n🎯 FINAL RESULT:")
    print("=" * 20)
    
    if not missing_fields:
        print("🎉 PERFECT: All required fields are present!")
        print("✅ Google Ads template is complete and ready for deployment.")
    else:
        print(f"⚠️ INCOMPLETE: {len(missing_fields)} required field(s) missing:")
        for field in missing_fields:
            print(f"   • {field}")
    
    print(f"\n💾 TEMPLATE JSON:")
    print("-" * 20)
    print(json.dumps(template, indent=2))

if __name__ == "__main__":
    test_google_ads_template()
