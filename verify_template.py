#!/usr/bin/env python3
"""
Quick verification of Google Ads template
"""

# Simulate the template function
def get_google_ads_template():
    return {
        "name": "Google Ads",
        "description": "Google Ads campaign performance data",
        "fields": [
            {"name": "Date", "type": "date"},
            {"name": "Campaign ID", "type": "singleLineText"},
            {"name": "Campaign Name", "type": "singleLineText"},
            {"name": "Cost", "type": "currency"},
            {"name": "Impressions", "type": "number"},
            {"name": "Clicks", "type": "number"},
            {"name": "Conversions", "type": "number"},
            {"name": "CTR", "type": "percent"},
            {"name": "CPC", "type": "currency"},
            {"name": "Conv. Rate", "type": "percent"},
            {"name": "Cost per Conv.", "type": "currency"}
        ]
    }

def main():
    print("🔍 GOOGLE ADS TEMPLATE VERIFICATION")
    print("=" * 50)
    
    # Required fields from user specification
    required_fields = [
        'Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks', 
        'Conversions', 'CTR', 'CPC', 'Conv. Rate', 'Cost per Conv.'
    ]
    
    # Get template
    template = get_google_ads_template()
    template_fields = [field['name'] for field in template['fields']]
    
    print(f"📋 Template: {template['name']}")
    print(f"📊 Total Fields: {len(template_fields)}")
    print()
    
    print("🎯 FIELD COMPARISON:")
    print("-" * 30)
    
    all_correct = True
    for i, required_field in enumerate(required_fields, 1):
        if required_field in template_fields:
            print(f"  {i:2d}. ✅ {required_field}")
        else:
            print(f"  {i:2d}. ❌ {required_field} (MISSING)")
            all_correct = False
    
    print()
    print("📋 TEMPLATE FIELDS:")
    print("-" * 20)
    for i, field in enumerate(template_fields, 1):
        print(f"  {i:2d}. {field}")
    
    print()
    if all_correct and len(required_fields) == len(template_fields):
        print("🎉 SUCCESS: Template matches requirements perfectly!")
    else:
        print("❌ ERROR: Template doesn't match requirements")
        
        missing = [f for f in required_fields if f not in template_fields]
        extra = [f for f in template_fields if f not in required_fields]
        
        if missing:
            print(f"   Missing: {missing}")
        if extra:
            print(f"   Extra: {extra}")

if __name__ == "__main__":
    main()
