#!/usr/bin/env python3
"""
Test script for Airtable integration functionality
Tests the complete workflow from API connection to client configuration generation
"""

import os
import sys
import json
import logging
from datetime import datetime

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from attrcust import AirtableAPIManager
except ImportError as e:
    print(f"Error importing AirtableAPIManager: {e}")
    print("Make sure you're running this from the correct directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AirtableIntegrationTester:
    """Test class for Airtable integration functionality"""
    
    def __init__(self):
        self.api_token = os.getenv('AIRTABLE_API_KEY')
        if not self.api_token:
            print("❌ AIRTABLE_API_KEY environment variable not set")
            print("Please set your Airtable API token:")
            print("export AIRTABLE_API_KEY='your_token_here'")
            sys.exit(1)
        
        self.manager = AirtableAPIManager(self.api_token)
        self.test_results = {}
    
    def test_connection(self):
        """Test basic API connection"""
        print("\n🔗 Testing Airtable API Connection...")
        
        success, message = self.manager.test_connection()
        self.test_results['connection'] = success
        
        if success:
            print(f"✅ Connection successful: {message}")
        else:
            print(f"❌ Connection failed: {message}")
        
        return success
    
    def test_list_bases(self):
        """Test listing available bases"""
        print("\n📋 Testing Base Discovery...")
        
        success, bases = self.manager.list_bases()
        self.test_results['list_bases'] = success
        
        if success:
            print(f"✅ Found {len(bases)} bases:")
            for i, base in enumerate(bases[:5]):  # Show first 5 bases
                print(f"   {i+1}. {base['name']} (ID: {base['id']}, Tables: {base['tables_count']})")
            
            if len(bases) > 5:
                print(f"   ... and {len(bases) - 5} more bases")
            
            # Store first base for schema testing
            if bases:
                self.test_base = bases[0]
                return True
        else:
            print(f"❌ Failed to list bases: {bases}")
        
        return success
    
    def test_base_schema(self):
        """Test getting base schema"""
        if not hasattr(self, 'test_base'):
            print("❌ No test base available for schema testing")
            return False
        
        print(f"\n📊 Testing Base Schema Discovery for: {self.test_base['name']}")
        
        success, tables = self.manager.get_base_schema(self.test_base['id'])
        self.test_results['base_schema'] = success
        
        if success:
            print(f"✅ Retrieved schema with {len(tables)} tables:")
            for table in tables:
                print(f"   • {table['name']} (ID: {table['id']}, Fields: {len(table['fields'])})")
                # Show first few fields
                for field in table['fields'][:3]:
                    print(f"     - {field['name']} ({field['type']})")
                if len(table['fields']) > 3:
                    print(f"     ... and {len(table['fields']) - 3} more fields")
        else:
            print(f"❌ Failed to get base schema: {tables}")
        
        return success
    
    def test_workspace_discovery(self):
        """Test workspace discovery"""
        print("\n🏢 Testing Workspace Discovery...")
        
        success, workspaces = self.manager.get_workspaces()
        self.test_results['workspaces'] = success
        
        if success:
            print(f"✅ Found {len(workspaces)} workspaces:")
            for workspace in workspaces:
                print(f"   • {workspace['name']} (ID: {workspace['id']}, Permission: {workspace['permission_level']})")
        else:
            print(f"❌ Failed to get workspaces: {workspaces}")
        
        return success
    
    def test_table_templates(self):
        """Test table template generation"""
        print("\n📋 Testing Table Templates...")
        
        try:
            # Test GHL template
            ghl_template = self.manager._get_ghl_table_template()
            print(f"✅ GHL template: {ghl_template['name']} with {len(ghl_template['fields'])} fields")
            
            # Test Google Ads template
            gads_template = self.manager._get_google_ads_table_template()
            print(f"✅ Google Ads template: {gads_template['name']} with {len(gads_template['fields'])} fields")
            
            # Test POS template
            pos_template = self.manager._get_pos_table_template()
            print(f"✅ POS template: {pos_template['name']} with {len(pos_template['fields'])} fields")
            
            # Test Meta Ads template
            meta_template = self.manager._get_meta_ads_table_template()
            print(f"✅ Meta Ads template: {meta_template['name']} with {len(meta_template['fields'])} fields")
            
            self.test_results['templates'] = True
            return True
            
        except Exception as e:
            print(f"❌ Template generation failed: {str(e)}")
            self.test_results['templates'] = False
            return False
    
    def test_client_config_generation(self):
        """Test client configuration generation"""
        print("\n⚙️ Testing Client Configuration Generation...")
        
        try:
            # Create mock base info for testing
            mock_base_info = {
                'id': 'test_base_id',
                'name': 'Test Client Base',
                'tables': [
                    {'id': 'tbl_ghl_test', 'name': 'GHL Leads'},
                    {'id': 'tbl_gads_test', 'name': 'Google Ads Data'},
                    {'id': 'tbl_pos_test', 'name': 'POS Sales Data'},
                    {'id': 'tbl_meta_test', 'name': 'Meta Ads Data'}
                ]
            }
            
            # Test configuration generation logic (without actually calling the GUI method)
            config = {
                "client_info": {
                    "client_id": "test_client",
                    "business_name": "Test Auto Shop",
                    "created_date": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                },
                "data_sources": {
                    "enabled_sources": ["ghl", "google_ads", "pos", "meta_ads"],
                    "disabled_sources": [],
                    "source_configs": {
                        "ghl": {
                            "table_id": "tbl_ghl_test",
                            "cache_ttl": 300,
                            "date_field": "Date Created"
                        },
                        "google_ads": {
                            "table_id": "tbl_gads_test",
                            "cache_ttl": 300,
                            "date_field": "Date"
                        },
                        "pos": {
                            "table_id": "tbl_pos_test",
                            "cache_ttl": 300,
                            "date_field": "Created"
                        },
                        "meta_ads": {
                            "table_id": "tbl_meta_test",
                            "cache_ttl": 300,
                            "date_field": "Reporting ends"
                        }
                    }
                },
                "airtable_configuration": {
                    "base_id": "test_base_id",
                    "api_key_env_var": "AIRTABLE_API_KEY"
                }
            }
            
            # Validate configuration structure
            required_keys = ['client_info', 'data_sources', 'airtable_configuration']
            for key in required_keys:
                if key not in config:
                    raise ValueError(f"Missing required key: {key}")
            
            print("✅ Client configuration generation successful")
            print(f"   • Client: {config['client_info']['business_name']}")
            print(f"   • Base ID: {config['airtable_configuration']['base_id']}")
            print(f"   • Enabled sources: {', '.join(config['data_sources']['enabled_sources'])}")
            
            self.test_results['config_generation'] = True
            return True
            
        except Exception as e:
            print(f"❌ Configuration generation failed: {str(e)}")
            self.test_results['config_generation'] = False
            return False
    
    def run_all_tests(self):
        """Run all tests and provide summary"""
        print("🚀 Starting Airtable Integration Tests")
        print("=" * 50)
        
        tests = [
            ('Connection Test', self.test_connection),
            ('Base Discovery', self.test_list_bases),
            ('Base Schema', self.test_base_schema),
            ('Workspace Discovery', self.test_workspace_discovery),
            ('Table Templates', self.test_table_templates),
            ('Config Generation', self.test_client_config_generation)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {str(e)}")
                self.test_results[test_name.lower().replace(' ', '_')] = False
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Airtable integration is working correctly.")
        else:
            print("⚠️ Some tests failed. Please check the errors above.")
        
        return passed == total

def main():
    """Main test function"""
    print("Airtable Integration Test Suite")
    print("Testing the complete workflow from API connection to client configuration")
    
    tester = AirtableIntegrationTester()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
