#!/usr/bin/env python3
"""
Debug script to test field validation accuracy
"""

import sys
import os
import logging
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Airtable manager
from attrcust import AirtableManager

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_field_validation():
    """Test field validation on a specific base"""
    
    # Initialize Airtable manager
    manager = AirtableManager()
    
    # Test with iGenius Repair base (from screenshot)
    test_base_id = "apptyeo8OtplQ8wAN"  # iGenius Repair
    
    print("🔍 FIELD VALIDATION DEBUG TEST")
    print("=" * 50)
    print(f"Testing base: {test_base_id}")
    
    try:
        # Get base schema
        print("\n📋 Getting base schema...")
        success, tables = manager.get_base_schema(test_base_id)
        
        if not success:
            print(f"❌ Failed to get base schema: {tables}")
            return
        
        print(f"✅ Retrieved {len(tables)} tables")
        
        # Required fields for Google Ads
        required_google_ads_fields = [
            'Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 
            'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords'
        ]
        
        # Find Google Ads table
        google_ads_table = None
        for table in tables:
            table_name = table['name'].lower()
            if any(keyword in table_name for keyword in ['google ads', 'google_ads', 'googleads', 'adwords', 'google']):
                google_ads_table = table
                break
        
        if google_ads_table:
            print(f"\n📊 GOOGLE ADS TABLE ANALYSIS")
            print(f"Table Name: {google_ads_table['name']}")
            print(f"Table ID: {google_ads_table['id']}")
            
            # Get existing fields
            existing_fields = [field['name'] for field in google_ads_table.get('fields', [])]
            print(f"\n✅ Existing Fields ({len(existing_fields)}):")
            for i, field in enumerate(existing_fields, 1):
                print(f"  {i:2d}. {field}")
            
            # Check for missing fields
            missing_fields = []
            for required_field in required_google_ads_fields:
                if required_field not in existing_fields:
                    missing_fields.append(required_field)
            
            print(f"\n🎯 REQUIRED FIELDS ({len(required_google_ads_fields)}):")
            for i, field in enumerate(required_google_ads_fields, 1):
                status = "✅" if field in existing_fields else "❌"
                print(f"  {i:2d}. {status} {field}")
            
            if missing_fields:
                print(f"\n❌ MISSING FIELDS ({len(missing_fields)}):")
                for i, field in enumerate(missing_fields, 1):
                    print(f"  {i:2d}. {field}")
                print(f"\n🎯 RESULT: Google Ads table is INCOMPLETE")
            else:
                print(f"\n🎉 RESULT: Google Ads table is COMPLETE")
        else:
            print("❌ No Google Ads table found")
        
        # Test all tables
        print(f"\n📋 ALL TABLES SUMMARY:")
        print("-" * 30)
        for i, table in enumerate(tables, 1):
            field_count = len(table.get('fields', []))
            print(f"{i:2d}. {table['name']} ({field_count} fields)")
            
            # Show first few fields
            fields = table.get('fields', [])[:5]
            for field in fields:
                print(f"     • {field['name']}")
            if len(table.get('fields', [])) > 5:
                print(f"     ... and {len(table.get('fields', [])) - 5} more")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_field_validation()
